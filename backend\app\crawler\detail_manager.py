#!/usr/bin/env python3
"""
详情管理器 - 统一管理各平台的商品详情获取
"""

import logging
import re
from typing import Dict, Optional, Any
from urllib.parse import urlparse, parse_qs

from app.crawler.platforms.taobao_detail import get_taobao_detail_crawler
from app.crawler.platforms.alibaba_1688 import get_alibaba_1688_crawler

logger = logging.getLogger(__name__)

class DetailManager:
    """详情管理器"""
    
    def __init__(self):
        self.crawlers = {
            "taobao": get_taobao_detail_crawler(),
            "tmall": get_taobao_detail_crawler(),  # 淘宝和天猫使用同一个爬虫
            "1688": get_alibaba_1688_crawler(),
            # TODO: 添加其他平台的爬虫
            # "jd": get_jd_detail_crawler(),
            # "pdd": get_pdd_detail_crawler(),
        }
        
        # URL模式匹配
        self.url_patterns = {
            "taobao": [
                r"item\.taobao\.com",
                r"detail\.taobao\.com",
                r"h5\.m\.taobao\.com"
            ],
            "tmall": [
                r"detail\.tmall\.com",
                r"item\.tmall\.com",
                r"h5\.m\.tmall\.com"
            ],
            "1688": [
                r"detail\.1688\.com",
                r"m\.1688\.com"
            ],
            "jd": [
                r"item\.jd\.com",
                r"item\.m\.jd\.com"
            ],
            "pdd": [
                r"mobile\.yangkeduo\.com",
                r"yangkeduo\.com"
            ]
        }
    
    async def get_product_detail(self, product_url: str) -> Dict[str, Any]:
        """
        获取商品详情
        
        Args:
            product_url: 商品链接
        
        Returns:
            商品详情结果
        """
        try:
            # 验证URL
            if not product_url or not product_url.strip():
                return {
                    "success": False,
                    "message": "商品链接不能为空",
                    "data": None
                }
            
            # 识别平台
            platform = self._identify_platform(product_url)
            if not platform:
                return {
                    "success": False,
                    "message": "无法识别商品链接的平台",
                    "data": None
                }
            
            # 检查平台是否支持
            if platform not in self.crawlers:
                return {
                    "success": False,
                    "message": f"暂不支持 {platform} 平台的商品详情获取",
                    "data": None
                }
            
            logger.info(f"开始获取商品详情: 平台={platform}, URL={product_url}")
            
            # 获取爬虫实例
            crawler = self.crawlers[platform]
            
            # 执行详情获取
            detail_result = await self._execute_get_detail(
                crawler=crawler,
                product_url=product_url,
                platform=platform
            )
            
            if detail_result.get("success"):
                logger.info(f"获取商品详情成功: {detail_result.get('data', {}).get('name', 'Unknown')}")
            else:
                logger.warning(f"获取商品详情失败: {detail_result.get('message')}")
            
            return detail_result
            
        except Exception as e:
            logger.error(f"获取商品详情失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取详情失败: {str(e)}",
                "data": None
            }
    
    def _identify_platform(self, url: str) -> Optional[str]:
        """识别URL对应的平台"""
        try:
            # 标准化URL
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            # 解析URL
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.lower()
            
            # 匹配平台
            for platform, patterns in self.url_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, domain):
                        return platform
            
            return None
            
        except Exception as e:
            logger.error(f"识别平台失败: {str(e)}")
            return None
    
    async def _execute_get_detail(
        self,
        crawler,
        product_url: str,
        platform: str
    ) -> Dict[str, Any]:
        """执行具体的详情获取操作"""
        try:
            # 根据平台调用不同的详情获取方法
            if platform in ["taobao", "tmall"]:
                result = await self._get_taobao_detail(crawler, product_url)
            elif platform == "1688":
                result = await self._get_1688_detail(crawler, product_url)
            else:
                return {
                    "success": False,
                    "message": f"平台 {platform} 详情获取功能暂未实现",
                    "data": None
                }
            
            return result
            
        except Exception as e:
            logger.error(f"执行详情获取失败: {str(e)}")
            return {
                "success": False,
                "message": f"详情获取执行失败: {str(e)}",
                "data": None
            }
    
    async def _get_taobao_detail(self, crawler, product_url: str) -> Dict[str, Any]:
        """获取淘宝/天猫商品详情"""
        try:
            # 调用淘宝详情爬虫
            result = crawler.get_product_detail_by_url_for_upstream(product_url)

            if result and isinstance(result, dict):
                return {
                    "success": True,
                    "message": "获取详情成功",
                    "data": result
                }
            else:
                return {
                    "success": False,
                    "message": "获取详情失败，返回数据格式错误",
                    "data": None
                }

        except Exception as e:
            logger.error(f"淘宝详情获取失败: {str(e)}")
            return {
                "success": False,
                "message": f"淘宝详情获取失败: {str(e)}",
                "data": None
            }
    
    async def _get_1688_detail(self, crawler, product_url: str) -> Dict[str, Any]:
        """获取1688商品详情"""
        try:
            # 调用1688详情爬虫
            result = await crawler.get_product_detail(product_url)

            if result and isinstance(result, dict):
                # 检查1688爬虫返回的数据结构
                if result.get("success") and result.get("data"):
                    # 1688爬虫返回的是包装格式: {"success": True, "data": {...}, "account_used": "...", "platform": "..."}
                    # 我们需要提取其中的data字段作为实际的商品详情
                    product_data = result.get("data")
                    return {
                        "success": True,
                        "message": "获取详情成功",
                        "data": product_data
                    }
                elif "id" in result and "name" in result:
                    # 如果直接返回的是商品详情数据
                    return {
                        "success": True,
                        "message": "获取详情成功",
                        "data": result
                    }
                else:
                    logger.error(f"1688爬虫返回数据格式异常: {result}")
                    return {
                        "success": False,
                        "message": "获取详情失败，返回数据格式错误",
                        "data": None
                    }
            else:
                return {
                    "success": False,
                    "message": "获取详情失败，返回数据格式错误",
                    "data": None
                }

        except Exception as e:
            logger.error(f"1688详情获取失败: {str(e)}")
            return {
                "success": False,
                "message": f"1688详情获取失败: {str(e)}",
                "data": None
            }
    
    def extract_product_id(self, url: str, platform: str) -> Optional[str]:
        """从URL中提取商品ID"""
        try:
            if platform in ["taobao", "tmall"]:
                # 淘宝/天猫商品ID提取
                match = re.search(r'[?&]id=(\d+)', url)
                if match:
                    return match.group(1)
            
            elif platform == "1688":
                # 1688商品ID提取
                match = re.search(r'/offer/(\d+)\.html', url)
                if match:
                    return match.group(1)
            
            elif platform == "jd":
                # 京东商品ID提取
                match = re.search(r'/(\d+)\.html', url)
                if match:
                    return match.group(1)
            
            elif platform == "pdd":
                # 拼多多商品ID提取
                parsed_url = urlparse(url)
                query_params = parse_qs(parsed_url.query)
                goods_id = query_params.get('goods_id', [None])[0]
                if goods_id:
                    return goods_id
            
            return None
            
        except Exception as e:
            logger.error(f"提取商品ID失败: {str(e)}")
            return None
    
    def get_supported_platforms(self) -> list:
        """获取支持的平台列表"""
        return list(self.crawlers.keys())
    
    def is_platform_supported(self, platform: str) -> bool:
        """检查平台是否支持"""
        return platform in self.crawlers
    
    def is_url_supported(self, url: str) -> bool:
        """检查URL是否支持"""
        platform = self._identify_platform(url)
        return platform is not None and self.is_platform_supported(platform)
