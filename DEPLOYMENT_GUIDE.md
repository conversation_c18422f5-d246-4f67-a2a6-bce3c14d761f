# AqentCrawler 部署指南

## 📋 目录
- [快速开始](#快速开始)
- [开发环境](#开发环境)
- [生产环境](#生产环境)
- [环境配置](#环境配置)
- [常见问题](#常见问题)

## 🚀 快速开始

### 一键启动
```bash
# 克隆项目后，运行快速启动脚本
python start.py
```

选择对应的启动模式：
- `1` - 开发环境 (推荐用于开发调试)
- `2` - 生产环境 (推荐用于正式部署)
- `5` - 安装依赖 (首次使用必选)

## 🛠️ 开发环境

### 系统要求
- Python 3.8+
- Node.js 16+
- MySQL 8.0+
- Redis 6.0+

### 安装依赖
```bash
# 方式1: 使用快速启动脚本
python start.py
# 选择 "5. 安装依赖"

# 方式2: 手动安装
pip install -r backend/requirements.txt
cd frontend && npm install
```

### 启动开发环境
```bash
# 方式1: 使用快速启动脚本
python start.py
# 选择 "1. 开发环境"

# 方式2: 使用开发脚本
python scripts/start_dev.py

# 方式3: 分别启动
# 后端
cd backend && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 前端 (新终端)
cd frontend && npm run dev
```

### 开发环境特性
- ✅ 自动热重载
- ✅ 详细错误信息
- ✅ 开发工具集成
- ✅ 实时日志输出
- ✅ 自动依赖检查

### 访问地址
- 前端界面: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

## 🏭 生产环境

### 系统要求
- Python 3.8+
- Node.js 16+
- MySQL 8.0+
- Redis 6.0+
- Nginx (推荐)
- Gunicorn

### 安装生产依赖
```bash
# 安装Gunicorn
pip install gunicorn

# 安装项目依赖
pip install -r backend/requirements.txt
```

### 环境配置
```bash
# 复制环境配置模板
cp .env.example backend/.env

# 编辑配置文件
vim backend/.env
```

必须配置的环境变量：
```bash
# 数据库
DATABASE_URL=mysql+pymysql://user:password@host:port/database

# Redis
REDIS_URL=redis://host:port/db

# 安全
JWT_SECRET_KEY=your-super-secret-key

# 管理员账号
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password
```

### 构建和启动
```bash
# 方式1: 使用生产脚本
python scripts/start_prod.py --build

# 方式2: 手动构建
cd frontend && npm run build
cd ../backend && gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### 生成配置文件
```bash
# 生成Nginx和systemd配置
python scripts/start_prod.py --config
```

### Nginx配置
```bash
# 复制生成的配置
sudo cp nginx.conf /etc/nginx/sites-available/aqentcrawler
sudo ln -s /etc/nginx/sites-available/aqentcrawler /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx
```

### Systemd服务
```bash
# 安装服务
sudo cp aqentcrawler.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable aqentcrawler
sudo systemctl start aqentcrawler

# 查看状态
sudo systemctl status aqentcrawler
```

## ⚙️ 环境配置

### 配置文件位置
- 主配置: `backend/.env`
- 模板文件: `.env.example`

### 核心配置项

#### 数据库配置
```bash
# MySQL
DATABASE_URL=mysql+pymysql://user:password@host:port/database

# 或分别配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_DATABASE=aqentcrawler
```

#### Redis配置
```bash
# 完整URL
REDIS_URL=redis://password@host:port/db

# 或分别配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

#### 安全配置
```bash
# JWT密钥 (必须修改)
JWT_SECRET_KEY=your-super-secret-jwt-key

# 管理员账号
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# Token有效期 (分钟)
ACCESS_TOKEN_EXPIRE_MINUTES=1440
```

#### 生产环境配置
```bash
# 环境标识
ENVIRONMENT=production

# 服务配置
WORKERS=4
HOST=0.0.0.0
PORT=8000

# 调试模式 (生产环境设为false)
DEBUG=false

# SQL调试配置
SQL_DEBUG=false          # 生产环境建议禁用
SQL_LOG_LEVEL=WARNING    # 生产环境建议使用WARNING或ERROR级别
```

#### SQL调试配置
```bash
# .env 文件配置（推荐方式）
# 注意：ENVIRONMENT由启动脚本自动设置，无需手动配置

# 开发环境 - 启用SQL调试
SQL_DEBUG=true
SQL_LOG_LEVEL=INFO

# 生产环境 - 禁用SQL调试
SQL_DEBUG=false
SQL_LOG_LEVEL=WARNING

# 生产环境问题排查 - 临时启用
# 方式1: 修改.env文件
SQL_DEBUG=true
SQL_LOG_LEVEL=ERROR

# 方式2: 设置环境变量（临时）
export SQL_DEBUG=true
export SQL_LOG_LEVEL=ERROR
```

**配置说明**：
- `ENVIRONMENT`: 由启动脚本自动设置（`python start.py` 选择1或2）
- `SQL_DEBUG`: 是否启用SQL语句打印，开发环境建议启用，生产环境建议禁用
- `SQL_LOG_LEVEL`: SQL日志级别，生产环境建议使用WARNING或ERROR

**环境变量优先级**：运行时环境变量 > 系统环境变量 > .env文件

详细使用说明请参考：[SQL调试指南](backend/docs/SQL_DEBUG_GUIDE.md)

## 🔧 常见问题

### Q: 首次启动失败？
A: 请确保：
1. 已安装所有依赖
2. 数据库和Redis服务正常运行
3. 环境配置正确

### Q: 前端无法访问后端API？
A: 检查：
1. 后端服务是否启动 (http://localhost:8000/health)
2. CORS配置是否正确
3. 防火墙设置

### Q: 数据库连接失败？
A: 检查：
1. 数据库服务是否运行
2. 连接参数是否正确
3. 数据库是否存在

### Q: Redis连接失败？
A: 检查：
1. Redis服务是否运行
2. 连接参数是否正确
3. 密码配置是否正确

### Q: 生产环境性能问题？
A: 优化建议：
1. 使用Nginx反向代理
2. 调整Gunicorn worker数量
3. 配置Redis缓存
4. 使用CDN加速静态资源

## 📚 更多资源

### 文档
- [API文档](backend/API_DOCUMENTATION.md)
- [项目说明](README.md)
- [架构文档](SIMPLIFIED_ARCHITECTURE.md)

### 默认账号
- 用户名: `admin`
- 密码: `admin123`

### 预置Token
系统预置了4个长期有效的API Token，供上游系统使用：
- `upstream_system_1`: 365天有效期，全权限
- `upstream_system_2`: 90天有效期，搜索+详情权限
- `test_system`: 30天有效期，全权限
- `dev_system`: 180天有效期，全权限

获取Token：
```bash
# 管理员登录后访问
GET /api/v1/auth/preset-tokens
```

### 技术支持
如遇问题，请检查：
1. 日志文件: `backend/logs/app.log`
2. 系统状态: `systemctl status aqentcrawler`
3. 服务健康: `curl http://localhost:8000/health`
